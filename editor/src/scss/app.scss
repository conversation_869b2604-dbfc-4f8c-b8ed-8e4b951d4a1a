@import "bootstrap";
@import 'allotment/dist/style.css';

:root {
  --ttw-foreground: #fafafa;
  --ttw-file-hover-background: #f8f9fa;
  --ttw-file-active-background: #f1f2f2;
  --ttw-file-disabled-color: #888;
  --ttw-editor-background: #FFF;
  --ttw-priview-background: #fcfcff;
  --ttw-dimmer-background: rgba(255, 255, 255, .85);
  --ttw-diff-number-color: rgba(0, 0, 0, 0.3);
  --ttw-diff-new-background: #ecfdf0;
  --ttw-diff-old-background: #fbe9eb;
  --ttw-message-background: rgb(243, 243, 243);
  --ttw-context-bar-background: #fafafa;
  --ttw-placeholder-color: rgba(54, 54, 54, .3);
  --ttw-shadow-color: rgba(0, 0, 0, 0.1);
  --ttw-tool-background: #ffffff;
  --ttw-tool-light-background: #f8f9fa;
}

[data-theme=dark] {
  --ttw-foreground: #24292E;
  --ttw-file-hover-background: #2c3239;
  --ttw-file-active-background: #282e34;
  --ttw-file-disabled-color: #666;
  --ttw-editor-background: #1F2428;
  --ttw-priview-background: #24292E;
  --ttw-dimmer-background: rgba(31, 36, 40, .85);
  --ttw-diff-number-color: rgba(232, 230, 227, 0.3);
  --ttw-diff-new-background: rgb(6, 58, 28);
  --ttw-diff-old-background: rgb(56, 10, 15);
  --ttw-message-background: #2f363d;
  --ttw-context-bar-background: #24292E;
  --ttw-placeholder-color: rgba(200, 200, 200, .5);
  --ttw-shadow-color: rgba(0, 0, 0, 0.3);
  --ttw-tool-background: #2f363d;
  --ttw-tool-light-background: #24292E;

  .popover {
    --bs-popover-bg: var(--ttw-box-background);
    --bs-popover-body-color: var(--ttw-box-color);
  }

  .nav-tabs {
    --bs-nav-tabs-border-color: var(--ttw-border-color);
    --bs-nav-tabs-link-active-bg: var(--ttw-background);
    --bs-nav-tabs-link-active-color: var(--ttw-color);
    --bs-nav-tabs-link-active-border-color: var(--ttw-border-color) var(--ttw-border-color) var(--ttw-background);
  }

  .form-control {
    background-color: var(--ttw-foreground);
    color: var(--ttw-color);
    border-color: var(--ttw-border-color);
  }

  .form-select {
    background-color: var(--ttw-foreground);
    color: var(--ttw-color);
    border-color: var(--ttw-border-color);
  }

  .modal {
    --bs-modal-bg: var(--ttw-background);
    --bs-modal-header-border-color: var(--ttw-border-color);
    --bs-modal-footer-border-color: var(--ttw-border-color);

    .modal-header, .modal-footer {
      background-color: var(--ttw-foreground);
    }
  }

  .btn-light {
    --bs-btn-bg: #2f363d;
    --bs-btn-border-color: #2f363d;
    --bs-btn-disabled-bg: #50575E;
    --bs-btn-disabled-border-color: #50575E;
    --bs-btn-color: var(--ttw-color);
    --bs-btn-hover-color: var(--ttw-color);
    --bs-btn-active-color: var(--ttw-color);
    --bs-btn-disabled-color: var(--ttw-color);
    --bs-btn-hover-bg: #3A4149;
    --bs-btn-hover-border-color: #343A41;
    --bs-btn-active-bg: #242A30;
    --bs-btn-active-border-color: #343A41;
  }

  .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
  }
}

:root {
  --reactour-accent: var(--bs-primary);
  --separator-border: var(--ttw-border-color);
  --focus-border: var(--bs-primary);
  --bs-body-bg: var(--ttw-background);
  --bs-border-color: var(--ttw-border-color);
}


.dropdown-menu {
  --bs-dropdown-bg: var(--ttw-box-background);
  --bs-dropdown-color: var(--ttw-box-color);
  --bs-dropdown-link-color: var(--ttw-box-color);
  --bs-dropdown-link-hover-bg: var(--ttw-box-hover-background);
  --bs-dropdown-link-hover-color: var(--ttw-box-color);
  --bs-dropdown-link-active-bg: var(--ttw-box-active-background);
  --bs-dropdown-link-active-color: var(--ttw-box-color);
}

html, body {
  font-size: 14px;
  color: var(--ttw-color);
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

a {
  color: inherit;
}


.bi {
  @keyframes icon-spin {
    to {
      transform: rotate(1turn);
    }
  }

  &.bi-spin {
    animation: icon-spin 1.5s steps(30) infinite;
  }
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  cursor: pointer;
  border-radius: 6px;
  background: rgba(0, 0, 0, .25);
  transition: color .2s ease;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, .1);
  border-radius: 0;
}

@import "notification";
@import "resizer";

.token.fences {
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
}
